from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    <PERSON><PERSON>an,
    Integer,
    ForeignKey,
)
from sqlalchemy.orm import relationship
from app.models.base import Base


class OrganizationSubscription(Base):
    __tablename__ = "organization_subscriptions"

    id = Column(String, primary_key=True)
    organization_id = Column(String, ForeignKey("organizations.id"), nullable=False, unique=True)
    subscription_plan_id = Column(String, ForeignKey("subscription_plans.id"), nullable=False)

    # Usage tracking for different limits
    interviews_used = Column(Integer, default=0)
    jobs_used = Column(Integer, default=0)  # Number of job applications created
    jd_used = Column(Integer, default=0)    # Number of job descriptions created
    candidate_suitability_used = Column(Integer, default=0)  # Number of candidate suitability checks performed

    activated_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)  # For future time-based subscriptions
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    organization = relationship("Organization", back_populates="subscription")
    subscription_plan = relationship("SubscriptionPlan", back_populates="organization_subscriptions")

    def __repr__(self):
        return f"<OrganizationSubscription {self.organization_id} - {self.interviews_used} interviews, {self.jobs_used} jobs, {self.jd_used} JDs, {self.candidate_suitability_used} suitability checks used>"

    @property
    def interviews_remaining(self):
        """Calculate remaining interviews based on plan limit and usage."""
        if self.subscription_plan:
            return max(0, self.subscription_plan.interview_limit - self.interviews_used)
        return 0

    @property
    def jobs_remaining(self):
        """Calculate remaining job applications based on plan limit and usage."""
        if self.subscription_plan:
            return max(0, self.subscription_plan.jobs_limit - self.jobs_used)
        return 0

    @property
    def jd_remaining(self):
        """Calculate remaining job descriptions based on plan limit and usage."""
        if self.subscription_plan:
            return max(0, self.subscription_plan.jd_limit - self.jd_used)
        return 0

    @property
    def candidate_suitability_remaining(self):
        """Calculate remaining candidate suitability checks based on plan limit and usage."""
        if self.subscription_plan and self.subscription_plan.candidate_suitability_limit:
            return max(0, self.subscription_plan.candidate_suitability_limit - self.candidate_suitability_used)
        return 0

    @property
    def is_interview_limit_reached(self):
        """Check if organization has reached their interview limit."""
        return self.interviews_remaining <= 0

    @property
    def is_jobs_limit_reached(self):
        """Check if organization has reached their jobs limit."""
        return self.jobs_remaining <= 0

    @property
    def is_jd_limit_reached(self):
        """Check if organization has reached their job description limit."""
        return self.jd_remaining <= 0

    @property
    def is_candidate_suitability_limit_reached(self):
        """Check if organization has reached their candidate suitability limit."""
        return self.candidate_suitability_remaining <= 0

    @property
    def is_limit_reached(self):
        """Check if organization has reached their interview limit (backward compatibility)."""
        return self.is_interview_limit_reached

    def can_schedule_interview(self):
        """Check if organization can schedule another interview."""
        return self.is_active and not self.is_interview_limit_reached

    def can_create_job(self):
        """Check if organization can create another job application."""
        return self.is_active and not self.is_jobs_limit_reached

    def can_create_jd(self):
        """Check if organization can create another job description."""
        return self.is_active and not self.is_jd_limit_reached

    def can_perform_candidate_suitability(self):
        """Check if organization can perform another candidate suitability check."""
        return self.is_active and not self.is_candidate_suitability_limit_reached

    def can_schedule_interview(self):
        """Check if organization can schedule another interview."""
        return self.is_active and not self.is_limit_reached