"""
Seed Subscription Plans

This script creates default subscription plans for both individual and organization users.
It creates free plans with limit of 1 for each feature as specified in the requirements.
"""

import uuid
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.subscription_plan import SubscriptionPlan, PlanType
from app.services.subscription_service import SubscriptionService
from app.schemas.subscription_schema import SubscriptionPlanCreate


def create_individual_free_plan(db: Session) -> SubscriptionPlan:
    """Create the Individual Free Plan."""
    plan_data = SubscriptionPlanCreate(
        name="Individual Free Plan",
        description="Free subscription plan for individual users with basic limits",
        plan_type=PlanType.individual,
        interview_limit=1,
        jobs_limit=1,
        jd_limit=1,
        resume_limit=1,
        candidate_suitability_limit=None,  # Not applicable for individuals
        price=0.00,
        is_active=True,
        features={
            "type": "individual_free",
            "support": "community",
            "features": [
                "1 Interview per month",
                "1 Job application per month", 
                "1 Job description per month",
                "1 Resume per month",
                "Community support"
            ]
        }
    )
    
    subscription_service = SubscriptionService(db)
    return subscription_service.create_subscription_plan(plan_data)


def create_organization_free_plan(db: Session) -> SubscriptionPlan:
    """Create the Organization Free Plan."""
    plan_data = SubscriptionPlanCreate(
        name="Organization Free Plan",
        description="Free subscription plan for organizations with basic limits",
        plan_type=PlanType.organization,
        interview_limit=1,
        jobs_limit=1,
        jd_limit=1,
        resume_limit=None,  # Not applicable for organizations
        candidate_suitability_limit=1,
        price=0.00,
        is_active=True,
        features={
            "type": "organization_free",
            "support": "community",
            "features": [
                "1 Interview per month",
                "1 Job posting per month",
                "1 Job description per month", 
                "1 Candidate suitability check per month",
                "Community support"
            ]
        }
    )
    
    subscription_service = SubscriptionService(db)
    return subscription_service.create_subscription_plan(plan_data)


def create_individual_premium_plan(db: Session) -> SubscriptionPlan:
    """Create a sample Individual Premium Plan."""
    plan_data = SubscriptionPlanCreate(
        name="Individual Premium Plan",
        description="Premium subscription plan for individual users with enhanced limits",
        plan_type=PlanType.individual,
        interview_limit=50,
        jobs_limit=100,
        jd_limit=25,
        resume_limit=10,
        candidate_suitability_limit=None,  # Not applicable for individuals
        price=29.99,
        is_active=True,
        features={
            "type": "individual_premium",
            "support": "priority",
            "features": [
                "50 Interviews per month",
                "100 Job applications per month",
                "25 Job descriptions per month",
                "10 Resumes per month",
                "Priority support",
                "Advanced analytics",
                "Custom branding"
            ]
        }
    )
    
    subscription_service = SubscriptionService(db)
    return subscription_service.create_subscription_plan(plan_data)


def create_organization_premium_plan(db: Session) -> SubscriptionPlan:
    """Create a sample Organization Premium Plan."""
    plan_data = SubscriptionPlanCreate(
        name="Organization Premium Plan",
        description="Premium subscription plan for organizations with enhanced limits",
        plan_type=PlanType.organization,
        interview_limit=200,
        jobs_limit=500,
        jd_limit=100,
        resume_limit=None,  # Not applicable for organizations
        candidate_suitability_limit=1000,
        price=199.99,
        is_active=True,
        features={
            "type": "organization_premium",
            "support": "dedicated",
            "features": [
                "200 Interviews per month",
                "500 Job postings per month",
                "100 Job descriptions per month",
                "1000 Candidate suitability checks per month",
                "Dedicated support",
                "Advanced analytics",
                "Custom branding",
                "Team collaboration tools",
                "API access"
            ]
        }
    )
    
    subscription_service = SubscriptionService(db)
    return subscription_service.create_subscription_plan(plan_data)


def seed_subscription_plans():
    """Seed the database with default subscription plans."""
    db = SessionLocal()
    
    try:
        print("🌱 Seeding subscription plans...")
        
        # Check if plans already exist
        subscription_service = SubscriptionService(db)
        existing_plans = subscription_service.get_all_subscription_plans(include_inactive=True)
        
        if existing_plans:
            print(f"⚠️  Found {len(existing_plans)} existing subscription plans. Skipping seed.")
            return
        
        # Create free plans
        print("📝 Creating Individual Free Plan...")
        individual_free = create_individual_free_plan(db)
        print(f"✅ Created: {individual_free.name}")
        
        print("📝 Creating Organization Free Plan...")
        organization_free = create_organization_free_plan(db)
        print(f"✅ Created: {organization_free.name}")
        
        # Create premium plans as examples
        print("📝 Creating Individual Premium Plan...")
        individual_premium = create_individual_premium_plan(db)
        print(f"✅ Created: {individual_premium.name}")
        
        print("📝 Creating Organization Premium Plan...")
        organization_premium = create_organization_premium_plan(db)
        print(f"✅ Created: {organization_premium.name}")
        
        print("🎉 Successfully seeded subscription plans!")
        
    except Exception as e:
        print(f"❌ Error seeding subscription plans: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    seed_subscription_plans()
